/* Main Layout - 25% / 75% Split - Full Width with Navbar */
.sales-page {
  height: calc(100vh - 48px); /* Navbar height'ı çıkar */
  width: 100vw;
  margin: 0;
  padding: 0;
  position: fixed;
  top: 48px; /* Navbar height'ı kadar a<PERSON> b<PERSON> */
  left: 0;
}

.main-layout {
  display: flex;
  height: calc(100vh - 48px); /* Navbar height'ı çıkar */
  width: 100vw;
  margin: 0;
  padding: 0;
}

/* Left Panel - Sales Screen (25%) */
.sales-panel {
  width: 50%;
  min-width: 300px;
  height: calc(100vh - 48px); /* Navbar height'ı çıkar */
  overflow-y: auto;
  background: linear-gradient(135deg, #f8f9fb 0%, #f1f3f7 100%);
  border-right: 2px solid #e5e7eb;
  padding: 1.2rem;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

/* Right Panel - Future Content (75%) */
.content-panel {
  width: 50%;
  height: calc(100vh - 48px); /* Navbar height'ı çıkar */
  overflow-y: auto;
  padding: 0 0.8rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fb 100%);
}

/* Panel Headers */

/* Tab Section Styling */
.tab-section {
  margin-bottom: 1.2rem;
  background: #ffffff;
  border-radius: 10px;
  box-shadow:
    0 3px 5px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  /* DROPDOWN İÇİN ÖNEMLİ: overflow visible */
  overflow: visible;
  position: relative;
  z-index: 1;
}

.tab-section .tabs {
  margin-bottom: 0 !important;
  border-bottom: 1px solid #e5e7eb;
}

.tab-section .tabs ul {
  border-bottom: none !important;
}

.tab-section .tabs li .tab-button {
  border: none;
  background: none;
  cursor: pointer;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6rem; /* DAHA FAZLA BOŞLUK */
  border-bottom: 2px solid transparent !important;
  padding: 1rem 1.2rem !important; /* BÜYÜK PADDING */
  font-size: 1.1rem !important; /* BÜYÜK FONT - INPUT İLE AYNI */
  font-weight: 600 !important; /* DAHA KALIN */
  color: #6b7280 !important;
  transition: all 0.2s ease !important;
}

/* TAB ikonları büyütme */
.tab-section .tabs li .tab-button .icon {
  font-size: 1.2rem !important; /* BÜYÜK İKON */
}

.tab-section .tabs li .tab-button .icon i {
  font-size: 1.2rem !important; /* BÜYÜK İKON İÇERİĞİ */
}

.tab-section .tabs li.is-active .tab-button {
  color: #1e3a8a !important;
  border-bottom-color: #1e3a8a !important;
  background: rgba(30, 58, 138, 0.05) !important;
}

.tab-section .tabs li .tab-button:hover {
  color: #1e3a8a !important;
  background: rgba(30, 58, 138, 0.03) !important;
}

.tab-section .tabs li .tab-button:focus {
  outline: 2px solid #1e3a8a;
  outline-offset: 2px;
}

.tab-content {
  padding: 1.2rem;
  /* DROPDOWN İÇİN ÖNEMLİ: overflow visible */
  overflow: visible;
  position: relative;
}

.search-tab-content {
  /* DROPDOWN İÇİN ÖNEMLİ: overflow visible */
  overflow: visible;
  position: relative;
  z-index: 10;
}

.search-tab-content .field {
  margin-bottom: 0.6rem;
  /* DROPDOWN İÇİN ÖNEMLİ: overflow visible */
  overflow: visible;
  position: relative;
}

/* Category Grid Styling */
.category-tab-content {
  padding: 0.8rem;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.8rem;
  max-width: 380px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.4rem;
  cursor: pointer;
  padding: 0.6rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
}

.category-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.category-image-placeholder {
  width: 42px;
  height: 42px;
  background: #e5e7eb;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 1.1rem;
  border: 2px dashed #d1d5db;
}

.category-name {
  font-size: 0.7rem;
  font-weight: 500;
  color: #374151;
  text-align: center;
}

/* Sales Table Section */
.sales-table-section {
  background: #ffffff;
  border-radius: 10px;
  padding: 1.2rem;
  box-shadow:
    0 3px 5px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

/* Content Area */

/* Responsive Design */
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }

  .sales-panel {
    width: 100%;
    height: 50vh;
    min-width: unset;
    padding: 0.8rem;
  }

  .content-panel {
    width: 100%;
    height: 50vh;
    padding: 0.8rem;
  }

  /* Mobile total amount styling */
  .total-amount-container {
    height: 36px;
  }

  .total-label {
    font-size: 0.85rem;
    min-width: 90px;
    padding: 0 0.8rem;
  }

  .total-amount {
    font-size: 0.95rem;
    min-width: 110px;
    padding: 0 0.8rem;
  }

  /* Mobile search input styling - GÜÇLÜ OVERRIDE */
  .autocomplete-container .input,
  .autocomplete-container .input.is-large,
  .control.has-icons-left .input {
    padding: 0.7rem 0.9rem 0.7rem 4.5rem !important;
    padding-left: 4.5rem !important;
    font-size: 1rem !important; /* BÜYÜK MOBİL FONT */
    height: 50px !important; /* BÜYÜK MOBİL YÜKSEKLİK */
  }

  /* Mobile Ekle butonu */
  .button.is-primary.is-large {
    height: 42px !important; /* MOBİL INPUT İLE AYNI YÜKSEKLİK */
    font-size: 0.85rem !important; /* MOBİL INPUT İLE AYNI FONT */
    padding: 0.6rem 0.94rem !important;
  }

  /* Mobile TAB'lar */
  .tab-section .tabs li .tab-button {
    padding: 0.77rem 0.94rem !important;
    font-size: 0.85rem !important; /* MOBİL INPUT İLE AYNI FONT */
  }

  .tab-section .tabs li .tab-button .icon,
  .tab-section .tabs li .tab-button .icon i {
    font-size: 0.94rem !important; /* MOBİL BÜYÜK İKON */
  }

  .autocomplete-container .icon.is-left,
  .control.has-icons-left .icon.is-left {
    left: 1.1rem !important;
    font-size: 1.2rem !important; /* BÜYÜK MOBİL İKON */
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 15 !important;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .total-amount-container {
    height: 32px;
  }

  .total-label {
    font-size: 0.75rem;
    min-width: 75px;
    padding: 0 0.6rem;
  }

  .total-amount {
    font-size: 0.85rem;
    min-width: 95px;
    padding: 0 0.6rem;
  }

  /* Extra small search input styling - GÜÇLÜ OVERRIDE */
  .autocomplete-container .input,
  .autocomplete-container .input.is-large,
  .control.has-icons-left .input {
    padding: 0.55rem 0.7rem 0.55rem 4rem !important;
    padding-left: 4rem !important;
    font-size: 0.9rem !important; /* BÜYÜK EXTRA SMALL FONT */
    height: 45px !important; /* BÜYÜK EXTRA SMALL YÜKSEKLİK */
  }

  /* Extra Small Ekle butonu */
  .button.is-primary.is-large {
    height: 38px !important; /* EXTRA SMALL INPUT İLE AYNI YÜKSEKLİK */
    font-size: 0.77rem !important; /* EXTRA SMALL INPUT İLE AYNI FONT */
    padding: 0.47rem 0.77rem !important;
  }

  /* Extra Small TAB'lar */
  .tab-section .tabs li .tab-button {
    padding: 0.55rem 0.77rem !important;
    font-size: 0.77rem !important; /* EXTRA SMALL INPUT İLE AYNI FONT */
  }

  .tab-section .tabs li .tab-button .icon,
  .tab-section .tabs li .tab-button .icon i {
    font-size: 0.85rem !important; /* EXTRA SMALL BÜYÜK İKON */
  }

  .autocomplete-container .icon.is-left,
  .control.has-icons-left .icon.is-left {
    left: 0.9rem !important;
    font-size: 1.1rem !important; /* BÜYÜK EXTRA SMALL İKON */
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 15 !important;
  }
}

/* Decimal alignment for currency values */
.decimal-aligned {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  text-align: right;
  display: inline-block;
  min-width: 100px;
  letter-spacing: 0.5px;
}

/* Table column alignment */
.table td.has-text-right {
  text-align: right;
  vertical-align: middle;
}

/* Table header alignment */
.table th.has-text-right {
  text-align: right;
}

/* Table cell vertical alignment */
.table td {
  vertical-align: middle !important;
}

.table th {
  vertical-align: middle !important;
}

/* Sales table column width optimization */
.sales-table {
  table-layout: fixed !important;
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.sales-table th,
.sales-table td {
  padding: 4px 6px !important;
  margin: 0 !important;
  border: 1px solid #e5e7eb !important;
  line-height: 1.2 !important;
  font-size: 0.9rem !important;
}

.sales-table th {
  padding: 6px 8px !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  background-color: #f8f9fa !important;
}

.sales-table tbody tr {
  height: auto !important;
  min-height: 32px !important;
}

.sales-table .col-sequence {
  width: 30px !important;
  min-width: 30px !important;
  max-width: 30px !important;
  text-align: center !important;
}

.sales-table .col-product {
  width: auto !important;
  min-width: 200px !important;
}

.sales-table .col-unit {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  text-align: center !important;
}

.sales-table .col-price {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
}

.sales-table .col-quantity {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
}

.sales-table .col-total {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
}

.sales-table .col-actions {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  text-align: center !important;
}

/* Table cell content alignment and width enforcement */
.sales-table tbody td:nth-child(1) {
  width: 60px !important;
  text-align: center !important;
}

.sales-table tbody td:nth-child(2) {
  width: auto !important;
  min-width: 180px !important;
}

.sales-table tbody td:nth-child(3) {
  width: 80px !important;
  text-align: center !important;
}

.sales-table tbody td:nth-child(4) {
  width: 100px !important;
  text-align: right !important;
}

.sales-table tbody td:nth-child(5) {
  width: 100px !important;
  text-align: center !important;
}

.sales-table tbody td:nth-child(6) {
  width: 100px !important;
  text-align: right !important;
}

.sales-table tbody td:nth-child(7) {
  width: 85px !important;
  text-align: center !important;
}

/* Compact quantity input styling */

.sales-table .quantity-btn {
  width: 16px !important;
  height: 16px !important;
  min-height: 16px !important;
  padding: 0 !important;
  margin: 0 !important;
  border: 1px solid #d1d5db !important;
  font-size: 9px !important;
  font-weight: bold !important;
  border-radius: 2px !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
  transition: all 0.15s ease !important;
}

/* Increment button (+ button) - Green */
.quantity-btn.increment,
.sales-table .quantity-btn.increment {
  background: #10b981 !important;
  color: white !important;
  border-color: #059669 !important;
}

.quantity-btn.increment:hover,
.sales-table .quantity-btn.increment:hover {
  background: #059669 !important;
  border-color: #047857 !important;
  transform: scale(1.05) !important;
}

/* Decrement button (- button) - Red */
.quantity-btn.decrement,
.sales-table .quantity-btn.decrement {
  background: #ef4444 !important;
  color: white !important;
  border-color: #dc2626 !important;
}

.quantity-btn.decrement:hover,
.sales-table .quantity-btn.decrement:hover {
  background: #dc2626 !important;
  border-color: #b91c1c !important;
  transform: scale(1.05) !important;
}

/* Override Bulma button styles for quantity buttons */
.button.quantity-btn {
  border-width: 1px !important;
  font-weight: bold !important;
  transition: all 0.2s ease !important;
}

.button.quantity-btn .icon {
  color: inherit !important;
}

/* Quantity controls vertical alignment */
.quantity-controls {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
}

.quantity-controls .control {
  display: flex !important;
  align-items: center !important;
}

.quantity-controls .button {
  height: 28px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.quantity-controls .input {
  height: 24px !important;
  width: 51px !important;
  text-align: center !important;
  font-family: 'Courier New', monospace !important;
  font-size: 0.72rem !important;
  padding: 3px 4px !important;
}

/* Compact delete button */

/* Compact table container */
.table-container {
  padding: 0 !important;
  margin: 0 !important;
  overflow-x: auto !important;
}

/* Remove extra spacing from Bulma table */
.sales-table.table {
  margin-bottom: 0 !important;
}

/* Compact table wrapper */

/* Total and Remaining Container */
.total-remaining-container {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  gap: 1rem !important;
  width: 100% !important;
}

/* Total Amount Container Styling */
.total-amount-container,
.tags.has-addons.total-amount-container {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 60px !important;
  border-radius: 10px !important;
  overflow: hidden !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
  margin: 0 !important;
  flex-shrink: 0 !important;
  width: calc(50% - 0.5rem) !important;
  flex: 1 !important;
}

/* Remaining Amount Container */
.remaining-amount-container,
.tags.has-addons.remaining-amount-container {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 60px !important;
  border-radius: 10px !important;
  overflow: hidden !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
  margin: 0 !important;
  flex-shrink: 0 !important;
  width: calc(50% - 0.5rem) !important;
  flex: 1 !important;
}

.total-label,
.tag.total-label,
.remaining-label,
.tag.remaining-label {
  background: linear-gradient(135deg, #800020 0%, #8b1538 100%) !important;
  color: white !important;
  font-weight: 700 !important;
  font-size: 1.3rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 1.2rem !important;
  border-radius: 10px 0 0 10px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
  height: 100% !important;
  vertical-align: middle !important;
  border: none !important;
  flex: 1 !important;
  margin: 0 !important;
  min-width: 0 !important;
  text-align: center !important;
}

.total-amount,
.tag.total-amount,
.remaining-amount-btn,
.tag.remaining-amount-btn {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%) !important;
  color: white !important;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace !important;
  font-weight: 800 !important;
  font-size: 1.4rem !important;
  letter-spacing: 0.6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 1.2rem !important;
  border-radius: 0 10px 10px 0 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
  height: 100% !important;
  transition: all 0.25s ease !important;
  vertical-align: middle !important;
  border: none !important;
  flex: 1 !important;
  margin: 0 !important;
  min-width: 0 !important;
  text-align: center !important;
  cursor: pointer !important;
}

/* Hover effects for total amount container */
.total-amount-container:hover .total-label,
.remaining-amount-container:hover .remaining-label {
  background: linear-gradient(135deg, #8b1538 0%, #a01b42 100%) !important;
  transform: scale(1.02);
}

.total-amount-container:hover .total-amount,
.remaining-amount-container:hover .remaining-amount-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%) !important;
  transform: scale(1.02);
}

.total-amount-container:hover,
.remaining-amount-container:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

/* Tag alignment for decimal values */
.tag.decimal-aligned {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  letter-spacing: 0.5px;
  text-align: right;
  justify-content: flex-end;
}

/* Autocomplete Styles */
.autocomplete-container {
  position: relative !important;
  width: 100% !important;
  max-width: 100% !important;
  z-index: 1000 !important;
}

.autocomplete-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  width: 100%;
  background: #ffffff;
  border: 2px solid #1e3a8a;
  border-radius: 8px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}

.autocomplete-item {
  padding: 1rem 1.2rem;
  cursor: pointer;
  border: none;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.25s ease;
  font-size: 0.95rem;
  background: #ffffff;
  margin: 0;
  position: relative;
  border-radius: 0;
  width: 100%;
  box-sizing: border-box;
}

.autocomplete-item:hover,
.autocomplete-item.is-selected {
  background: linear-gradient(135deg, rgba(30, 58, 138, 0.06) 0%, rgba(128, 0, 32, 0.06) 100%);
  border-left: 3px solid #1e3a8a;
  border-bottom: 1px solid #f1f5f9;
  padding-left: calc(1.2rem - 3px);
  transform: translateX(0);
  box-shadow: none;
}

.autocomplete-item:last-child {
  border-bottom: none;
  border-radius: 0 0 5px 5px;
}

.autocomplete-item:first-child {
  border-radius: 5px 5px 0 0;
}

.autocomplete-item-content {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.autocomplete-item-name {
  font-size: 1rem;
  color: #1f2937;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.2rem;
  width: 100%;
  word-wrap: break-word;
}

.autocomplete-item-details {
  display: flex;
  gap: 0.6rem;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  justify-content: flex-start;
}

.autocomplete-item-details .tag {
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 5px;
  padding: 0.2rem 0.5rem;
  border: 1px solid transparent;
  transition: all 0.15s ease;
}

.autocomplete-item-details .tag.is-primary {
  background: linear-gradient(135deg, rgba(128, 0, 32, 0.1) 0%, rgba(139, 21, 56, 0.1) 100%);
  color: #800020;
  border-color: rgba(128, 0, 32, 0.2);
}

/* Prevent dropdown from affecting layout */
.autocomplete-dropdown {
  position: absolute !important;
  z-index: 9999 !important;
}

/* Search section adjustments */

/* Input field styling with left icon - GÜÇLÜ OVERRIDE */
.autocomplete-container .input,
.autocomplete-container .input.is-large,
.control.has-icons-left .input {
  border-radius: 6px !important;
  border: 2px solid #e5e7eb !important;
  transition: all 0.25s ease !important;
  font-size: 0.94rem !important; /* BÜYÜK FONT */
  padding: 0.77rem 0.94rem 0.77rem 4.08rem !important; /* BÜYÜK PADDING */
  padding-left: 4.08rem !important;
  height: 44px !important; /* BÜYÜK YÜKSEKLİK */
}

/* Ekle butonu input ile aynı yükseklikte */
.button.is-primary.is-large {
  height: 44px !important; /* INPUT İLE AYNI YÜKSEKLİK */
  font-size: 0.94rem !important; /* INPUT İLE AYNI FONT */
  padding: 0.77rem 1.1rem !important;
  border-radius: 6px !important;
}

.autocomplete-container .input:focus {
  border-color: #1e3a8a;
  border-radius: 6px;
  box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.1);
  outline: none;
}

/* When dropdown is visible, keep input separate */
.autocomplete-container.has-dropdown .input {
  border-radius: 6px;
  border-color: #1e3a8a;
  box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.1);
}

/* Search icon positioning - GÜÇLÜ OVERRIDE */
.autocomplete-container .icon.is-left,
.control.has-icons-left .icon.is-left,
.field .control .icon.is-left {
  position: absolute !important;
  left: 1.5rem !important; /* DAHA SOL */
  top: 50% !important;
  transform: translateY(-50%) !important;
  color: #1e3a8a !important;
  pointer-events: none !important;
  z-index: 15 !important; /* DAHA YÜKSEK Z-INDEX */
  font-size: 1.3rem !important; /* ÇOK BÜYÜK İKON */
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: auto !important;
  height: auto !important;
}

/* Icon visibility on all input states */
.autocomplete-container .input:hover + .icon.is-left,
.autocomplete-container .input:focus + .icon.is-left,
.autocomplete-container .input:active + .icon.is-left {
  color: #1e3a8a !important;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 10 !important;
}

/* Ensure icon is always visible in all states */
.autocomplete-container .icon.is-left i {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
  display: inline-block !important;
}

/* Override any Bulma hover states that might hide the icon */
.autocomplete-container:hover .icon.is-left,
.autocomplete-container .control:hover .icon.is-left {
  opacity: 1 !important;
  visibility: visible !important;
  color: #1e3a8a !important;
  z-index: 10 !important;
}

/* POS Payment System Styles */
.payment-header {
  background: linear-gradient(135deg, #f8f9fb 0%, #ffffff 100%);
  border-radius: 10px;
  padding: 1.2rem;
  margin-bottom: 1.2rem;
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.payment-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.6rem;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.summary-label {
  font-size: 1rem; /* BÜYÜK LABEL */
  color: #6b7280;
  font-weight: 600; /* DAHA KALIN */
}

.summary-value {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-weight: 800; /* ÇOK KALIN */
  font-size: 1.2rem; /* BÜYÜK DEĞER */
}

.summary-value.total {
  color: #1e3a8a;
}

.summary-value.discount {
  color: #f59e0b;
}

.summary-value.paid {
  color: #10b981;
}

.summary-value.remaining {
  color: #ef4444;
}

.payment-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 1.2rem;
  height: calc(100vh - 280px);
  max-height: calc(100vh - 280px);
}

/* POS Payment Methods - Isolated */
.content-panel .payment-methods {
  display: flex !important;
  flex-direction: column !important;
  gap: 1.5rem !important;
  position: relative !important;
}

.content-panel .payment-btn {
  padding: 0.68rem !important;
  border: none !important;
  border-radius: 6px !important;
  font-size: 0.7rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  text-align: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  height: 65px;
}

.content-panel .payment-btn.cash {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
}

.content-panel .payment-btn.credit-card {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  color: white !important;
}

.content-panel .payment-btn.meal-card {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  color: white !important;
}

.content-panel .payment-btn.cash-drawer {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  color: white !important;
}

.content-panel .payment-btn.installment {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
  color: white !important;
}

.content-panel .payment-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

/* Active/Selected payment button styles */
.content-panel .payment-btn.is-active,
.content-panel .payment-btn.is-selected {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  border: 2px solid #ffffff !important;
  outline: 2px solid #1e40af !important;
  outline-offset: 1px !important;
}

.content-panel .payment-btn.cash.is-active,
.content-panel .payment-btn.cash.is-selected {
  outline-color: #059669 !important;
}

.content-panel .payment-btn.credit-card.is-active,
.content-panel .payment-btn.credit-card.is-selected {
  outline-color: #1e40af !important;
}

.content-panel .payment-btn.meal-card.is-active,
.content-panel .payment-btn.meal-card.is-selected {
  outline-color: #d97706 !important;
}

.content-panel .payment-btn.cash-drawer.is-active,
.content-panel .payment-btn.cash-drawer.is-selected {
  outline-color: #dc2626 !important;
}

.content-panel .payment-btn.installment.is-active,
.content-panel .payment-btn.installment.is-selected {
  outline-color: #7c3aed !important;
}

/* POS Numeric Keypad - Isolated from simple-keyboard */
.content-panel .numeric-keypad {
  display: flex !important;
  flex-direction: column !important;
  gap: 0.8rem !important;
  position: relative !important;
  z-index: 1 !important;
}

.content-panel .amount-display {
  background: #1f2937 !important;
  border-radius: 6px !important;
  padding: 0.68rem !important;
  border: 2px solid #374151 !important;
}

.content-panel .amount-input {
  width: 100% !important;
  background: transparent !important;
  border: none !important;
  color: white !important;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace !important;
  font-size: 1.36rem !important;
  font-weight: 700 !important;
  text-align: center !important;
  outline: none !important;
}

.content-panel .amount-input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

.content-panel .keypad-table {
  width: 100% !important;
  border-collapse: separate !important;
  border-spacing: 2px !important;
  position: relative !important;
  padding: 0 !important;
  margin: 0 !important;
}

.content-panel .keypad-row {
  height: 75px !important;
}

.content-panel .keypad-table td {
  padding: 1px !important;
  margin: 0 !important;
  border: none !important;
  vertical-align: middle !important;
  text-align: center !important;
}

.content-panel .key-btn {
  background: #374151 !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.15s ease !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 60px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  margin: 0 !important;
  padding: px !important;
  box-sizing: border-box !important;
}

.content-panel .key-btn:hover {
  background: #4b5563 !important;
  transform: scale(1.02) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.content-panel .key-btn:active {
  transform: scale(0.98) !important;
}

.content-panel .key-btn.backspace {
  background: #ef4444 !important;
}

.content-panel .key-btn.backspace:hover {
  background: #dc2626 !important;
}

.content-panel .key-btn.clear {
  background: #f59e0b !important;
}

.content-panel .key-btn.clear:hover {
  background: #d97706 !important;
}

.content-panel .key-btn.decimal {
  background: #6b7280 !important;
  color: white !important;
  font-size: 1.1rem !important;
  font-weight: 700 !important;
}

.content-panel .key-btn.decimal:hover {
  background: #4b5563 !important;
}

/* Responsive keypad spacing - consistent across all resolutions */
@media screen and (max-width: 1377px) {
  .content-panel .keypad-table {
    border-spacing: 1px !important;
  }
  .content-panel .keypad-row {
    height: 70px !important;
  }
  .content-panel .key-btn {
    min-height: 56px !important;
    font-size: 0.95rem !important;
  }
}

@media screen and (max-width: 1024px) {
  .content-panel .keypad-table {
    border-spacing: 1px !important;
  }
  .content-panel .keypad-row {
    height: 65px !important;
  }
  .content-panel .key-btn {
    min-height: 52px !important;
    font-size: 0.9rem !important;
  }
}

@media screen and (max-width: 768px) {
  .content-panel .keypad-table {
    border-spacing: 1px !important;
  }
  .content-panel .keypad-row {
    height: 60px !important;
  }
  .content-panel .key-btn {
    min-height: 48px !important;
    font-size: 0.85rem !important;
  }
}

@media screen and (min-width: 1920px) {
  .content-panel .keypad-table {
    border-spacing: 2px !important;
  }
  .content-panel .keypad-row {
    height: 85px !important;
  }
  .content-panel .key-btn {
    min-height: 70px !important;
    font-size: 1.1rem !important;
  }
}

@media screen and (min-width: 2560px) {
  .content-panel .keypad-table {
    border-spacing: 3px !important;
  }
  .content-panel .keypad-row {
    height: 90px !important;
  }
  .content-panel .key-btn {
    min-height: 80px !important;
    font-size: 1.2rem !important;
  }
}

/* Sale Buttons Container */
.content-panel .sale-buttons {
  display: flex !important;
  gap: 0.8rem !important;
  margin-top: 0.3rem !important;
}

.content-panel .complete-sale-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.68rem !important;
  font-size: 0.85rem !important;
  font-weight: 700 !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  box-shadow: 0 3px 6px rgba(16, 185, 129, 0.2) !important;
  flex: 2 !important;
  height: 64px;
}

.complete-sale-btn2 {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.68rem !important;
  font-size: 0.85rem !important;
  font-weight: 700 !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  box-shadow: 0 3px 6px rgba(16, 185, 129, 0.2) !important;
  height: 100px;
}

.content-panel .demo-sale-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.68rem !important;
  font-size: 0.77rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.2) !important;
  flex: 1 !important;
  height: 64px;
}

.content-panel .pair-device-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.68rem !important;
  font-size: 0.77rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  box-shadow: 0 3px 6px rgba(245, 158, 11, 0.2) !important;
  flex: 1 !important;
  height: 64px;
}

.content-panel .sales-list-btn {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.68rem !important;
  font-size: 0.77rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  box-shadow: 0 3px 6px rgba(139, 92, 246, 0.2) !important;
  flex: 1 !important;
  height: 64px;
}

.content-panel .complete-sale-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3) !important;
}

.content-panel .demo-sale-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3) !important;
}

.content-panel .pair-device-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3) !important;
}

.content-panel .sales-list-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.3) !important;
}

/* POS Quick Actions - Isolated */
.content-panel .quick-actions {
  display: flex !important;
  flex-direction: column !important;
  gap: 1.5rem !important;
  position: relative !important;
}

.content-panel .action-btn {
  padding: 0.68rem !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 6px !important;
  background: white !important;
  font-size: 0.68rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.25s ease !important;
  text-align: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

.content-panel .action-btn.loyalty {
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
}

.content-panel .action-btn.premium {
  border-color: #8b5cf6 !important;
  color: #8b5cf6 !important;
}

.content-panel .action-btn.gift {
  border-color: #f59e0b !important;
  color: #f59e0b !important;
}

.content-panel .action-btn.receipt {
  border-color: #6b7280 !important;
  color: #6b7280 !important;
}

.content-panel .action-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
}

.content-panel .action-btn.loyalty:hover {
  background: #3b82f6 !important;
  color: white !important;
}

.content-panel .action-btn.premium:hover {
  background: #8b5cf6 !important;
  color: white !important;
}

.content-panel .action-btn.gift:hover {
  background: #f59e0b !important;
  color: white !important;
}

.content-panel .action-btn.receipt:hover {
  background: #6b7280 !important;
  color: white !important;
}

.content-panel .action-btn.is-warning {
  border-color: #f59e0b !important;
  color: #f59e0b !important;
}

.content-panel .action-btn.is-warning:hover {
  background: #f59e0b !important;
  color: white !important;
}

.content-panel .action-btn.is-info {
  border-color: #3b82f6 !important;
  color: #3b82f6 !important;
}

.content-panel .action-btn.is-info:hover {
  background: #3b82f6 !important;
  color: white !important;
}

/* Smooth scrollbar for dropdown */
.autocomplete-dropdown::-webkit-scrollbar {
  width: 5px;
}

.autocomplete-dropdown::-webkit-scrollbar-track {
  background: #f1f3f7;
  border-radius: 0 2px 2px 0;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #800020 0%, #1e3a8a 100%);
  border-radius: 2px;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #8b1538 0%, #3b82f6 100%);
}

/* Enhanced Split Payments Styling */
.split-payments-container {
  margin-top: 1rem;
  padding: 1.2rem;
  background: linear-gradient(135deg, #f8f9fb 0%, #f1f3f7 100%);
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.split-payments-header {
  margin-bottom: 1rem;
}

.split-payments-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
  color: #1f2937;
  font-size: 1rem;
  font-weight: 700;
}

.split-payments-title .icon {
  color: #1e3a8a;
}

.payment-progress {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.85rem;
  font-weight: 600;
  color: #374151;
  min-width: 120px;
  text-align: right;
}

.split-payments-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.split-payment-item {
  background: white;
  border-radius: 10px;
  border: 2px solid #e5e7eb;
  padding: 1rem;
  transition: all 0.2s ease;
}

.split-payment-item:hover {
  border-color: #1e3a8a;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.payment-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.payment-method-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.payment-method-badge.cash {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.payment-method-badge.credit-card {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

.payment-method-badge.meal-card {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.method-icon {
  font-size: 1rem;
}

.method-name {
  font-size: 0.85rem;
}

.payment-amount-display {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.payment-amount-display .amount {
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  font-family: 'Courier New', monospace;
}

.payment-actions {
  display: flex;
  gap: 0.4rem;
}

.btn-remove {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  transition: all 0.2s ease;
  background: #ef4444;
  color: white;
}

.btn-remove:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.payment-timestamp {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: right;
}

.split-payments-summary {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-row .label {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

.summary-row .value {
  font-size: 1rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
}

.summary-row.total-paid .value {
  color: #059669;
}

.summary-row.remaining .value {
  color: #dc2626;
}

.summary-row.remaining.completed .value {
  color: #059669;
}

.completion-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.8rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 0.8rem;
}

.split-payments-actions {
  display: flex;
  justify-content: center;
}

.btn-clear-all {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-clear-all:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Payment Complete State */
.summary-value.remaining.complete {
  color: #059669 !important;
  font-weight: 600 !important;
}

/* Remaining Amount Button Specific Styles */
.remaining-amount-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);
}

.remaining-amount-btn:disabled {
  background: #9ca3af !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
}

.remaining-amount-btn.active {
  background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Cash Change Modal Styles */
.cash-payment-info {
  padding: 1.5rem 0.8rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
}

.cash-payment-info .field {
  margin-bottom: 0;
  width: 100%;
  max-width: 350px;
  text-align: center;
}

.cash-payment-info .label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.6rem;
  text-align: center;
}

.cash-payment-info .control {
  display: flex;
  justify-content: center;
}

.cash-payment-info .tags.has-addons {
  justify-content: center;
  margin: 0;
}

.cash-payment-info .tag {
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 1.28rem;
  font-weight: 700;
  padding: 0.85rem 1.7rem;
  border-radius: 10px;
  min-width: 145px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 51px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.cash-payment-info .tag:first-child {
  background: #6b7280 !important;
  color: white !important;
}

.cash-payment-info .tag.is-primary {
  background: #3b82f6 !important;
  color: white !important;
}

.cash-payment-info .tag.is-success {
  background: #10b981 !important;
  color: white !important;
}

.cash-payment-info .notification {
  margin-top: 0.8rem;
  border-radius: 6px;
  width: 100%;
  max-width: 350px;
}

.modal-card-foot .button.is-success {
  font-size: 0.94rem;
  font-weight: 600;
  padding: 0.51rem 1.28rem;
}

.modal-card-foot .button.is-success:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
